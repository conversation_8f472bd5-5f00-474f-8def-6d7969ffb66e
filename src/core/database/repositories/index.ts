/**
 * Repository exports for Redis-based data access layer
 */

// Base repository
export * from './base-redis.repository';
export * from './base.repository';

// Repository factory
export * from './repository.factory';

// Specific entity repositories
export * from './agent.repository';
export * from './guild.repository';
export * from './session.repository';
export * from './user.repository';

// Repository types
export * from '../types/repository.types';

// Re-export commonly used base types
export type {
    BaseRedisRepository, BulkOperation, ComparisonOperator, EntityEvent, EntityKeyGenerator, ExportOptions, ExtendedRedisRepository, HealthCheckRepository, ImportOptions, MigrationRepository, QueryBuilder, QueryableRepository, RepositoryFactory, RepositoryManager, RepositoryOptions
} from '../types/repository.types';

// Repository utility functions
export const RepositoryUtils = {
  /**
   * Create a key generator for an entity
   */
  createKeyGenerator: <T extends import('../types').BaseEntity>(
    entityName: string,
    indexFields?: Array<keyof T>
  ): EntityKeyGenerator<T> => {
    const entityKey = entityName.toLowerCase();
    
    const generator: EntityKeyGenerator<T> = {
      primary: (id: string) => `${entityKey}:${id}`,
      pattern: `${entityKey}:*`,
      index: {} as Partial<Record<keyof T, string>>,
      search: `search:${entityKey}`,
      byField: (field: keyof T, value: any) => `${entityKey}:${String(field)}:${value}`,
    };

    // Add index patterns for specified fields
    if (indexFields) {
      for (const field of indexFields) {
        generator.index[field] = `idx:${entityKey}:${String(field)}`;
      }
    }

    return generator;
  },

  /**
   * Create default repository options
   */
  createDefaultOptions: (overrides: Partial<RepositoryOptions> = {}): RepositoryOptions => ({
    enableCaching: false,
    defaultTTL: 3600,
    enableSoftDelete: false,
    enableVersioning: false,
    enableAudit: false,
    compressionEnabled: false,
    serializationStrategy: 'json',
    indexFields: [],
    searchFields: [],
    ...overrides,
  }),

  /**
   * Validate repository options
   */
  validateOptions: (options: RepositoryOptions): boolean => {
    if (options.defaultTTL && options.defaultTTL < 0) {
      throw new Error('Default TTL must be positive');
    }

    if (options.serializationStrategy && 
        !['json', 'msgpack', 'protobuf'].includes(options.serializationStrategy)) {
      throw new Error('Invalid serialization strategy');
    }

    return true;
  },
};

/**
 * Repository factory for creating repository instances
 */
export class RedisRepositoryFactory implements RepositoryFactory {
  constructor(private readonly redisClient: import('ioredis').Redis) {}

  /**
   * Create a basic repository instance
   */
  create<T extends import('../types').BaseEntity>(
    entityName: string,
    keyGenerator: EntityKeyGenerator<T>,
    options: RepositoryOptions = {}
  ): BaseRedisRepository<T> {
    const { BaseRedisRepositoryImpl } = require('./base-redis.repository');
    const redisClient = this.redisClient;
    return new (class extends BaseRedisRepositoryImpl<T> {
      constructor() {
        super(redisClient, entityName, keyGenerator, options);
      }
    })();
  }

  /**
   * Create an extended repository instance with additional features
   */
  createExtended<T extends import('../types').BaseEntity>(
    entityName: string,
    keyGenerator: EntityKeyGenerator<T>,
    options: RepositoryOptions = {}
  ): ExtendedRedisRepository<T> {
    // This would be implemented with additional features like caching, bulk operations, etc.
    throw new Error('Extended repository implementation not yet available');
  }
}
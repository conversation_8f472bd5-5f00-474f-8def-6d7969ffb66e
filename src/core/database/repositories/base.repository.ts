import { Injectable } from '@nestjs/common';
import { RedisQuery, RedisService, RedisTransaction } from '../redis.service';
import { BaseEntity, CreateEntity, UpdateEntity } from '../types/base.interface';

// Remove this interface as it's now imported from types

export interface Repository<T extends BaseEntity> {
  create(data: CreateEntity<T>): Promise<T>;
  createWithId(data: CreateEntity<T>, id: string): Promise<T>;
  findById(id: string): Promise<T | null>;
  find(query?: RedisQuery<T>): Promise<ReadonlyArray<T>>;
  findOne(query?: RedisQuery<T>): Promise<T | null>;
  update(id: string, updates: UpdateEntity<T>): Promise<T | null>;
  delete(id: string, soft?: boolean): Promise<boolean>;
  count(query?: RedisQuery<T>): Promise<number>;
  exists(id: string): Promise<boolean>;
  
  // Batch operations
  createMany(entities: ReadonlyArray<CreateEntity<T>>): Promise<ReadonlyArray<T>>;
  updateMany(updates: ReadonlyArray<{ readonly id: string; readonly data: UpdateEntity<T> }>): Promise<ReadonlyArray<T>>;
  deleteMany(ids: ReadonlyArray<string>, soft?: boolean): Promise<boolean>;
  
  // Advanced queries
  findByField<K extends keyof T>(field: K, value: T[K]): Promise<ReadonlyArray<T>>;
  findByFields(fields: Partial<T>): Promise<ReadonlyArray<T>>;
  
  // Aggregation
  groupBy<K extends keyof T>(field: K): Promise<Readonly<Record<string, ReadonlyArray<T>>>>;
  
  // Relations (for entities with relationships)
  findWithRelations?(id: string, relations: ReadonlyArray<string>): Promise<T | null>;
}

@Injectable()
export abstract class BaseRepository<T extends BaseEntity> implements Repository<T> {
  constructor(
    protected readonly redisService: RedisService,
    protected readonly entityType: string
  ) {}

  /**
   * Create a new entity with auto-generated ID
   */
  async create(data: CreateEntity<T>): Promise<T> {
    return this.redisService.create<T>(this.entityType, data);
  }

  /**
   * Create a new entity with custom ID
   */
  async createWithId(data: CreateEntity<T>, customId: string): Promise<T> {
    return this.redisService.create<T>(this.entityType, data, customId);
  }

  /**
   * Find entity by ID
   */
  async findById(id: string): Promise<T | null> {
    return this.redisService.findById<T>(this.entityType, id);
  }

  /**
   * Find entities with optional query
   */
  async find(query: RedisQuery<T> = {}): Promise<ReadonlyArray<T>> {
    return this.redisService.find<T>(this.entityType, query);
  }

  /**
   * Find first entity matching query
   */
  async findOne(query: RedisQuery<T> = {}): Promise<T | null> {
    return this.redisService.findOne<T>(this.entityType, query);
  }

  /**
   * Update entity by ID
   */
  async update(id: string, updates: UpdateEntity<T>): Promise<T | null> {
    return this.redisService.update<T>(this.entityType, id, updates);
  }

  /**
   * Delete entity by ID
   */
  async delete(id: string, soft = true): Promise<boolean> {
    return this.redisService.delete(this.entityType, id, soft);
  }

  /**
   * Count entities
   */
  async count(query: RedisQuery<T> = {}): Promise<number> {
    return this.redisService.count(this.entityType, query);
  }

  /**
   * Check if entity exists
   */
  async exists(id: string): Promise<boolean> {
    const entity = await this.findById(id);
    return entity !== null;
  }

  /**
   * Create multiple entities
   */
  async createMany(entities: ReadonlyArray<CreateEntity<T>>): Promise<ReadonlyArray<T>> {
    const created: T[] = [];
    
    for (const entity of entities) {
      created.push(await this.create(entity));
    }
    
    return created;
  }

  /**
   * Update multiple entities
   */
  async updateMany(updates: ReadonlyArray<{ readonly id: string; readonly data: UpdateEntity<T> }>): Promise<ReadonlyArray<T>> {
    const updated: T[] = [];
    
    for (const { id, data } of updates) {
      const result = await this.update(id, data);
      if (result) {
        updated.push(result);
      }
    }
    
    return updated;
  }

  /**
   * Delete multiple entities
   */
  async deleteMany(ids: ReadonlyArray<string>, soft = true): Promise<boolean> {
    for (const id of ids) {
      await this.delete(id, soft);
    }
    return true;
  }

  /**
   * Find entities by a specific field value
   */
  async findByField<K extends keyof T>(field: K, value: T[K]): Promise<ReadonlyArray<T>> {
    return this.find({
      where: { [field as string]: value } as unknown as Partial<T>
    });
  }

  /**
   * Find entities by multiple field values
   */
  async findByFields(fields: Partial<T>): Promise<ReadonlyArray<T>> {
    return this.find({
      where: fields
    });
  }

  /**
   * Group entities by a field value
   */
  async groupBy<K extends keyof T>(field: K): Promise<Readonly<Record<string, ReadonlyArray<T>>>> {
    const entities = await this.find();
    const grouped: Record<string, T[]> = {};
    
    for (const entity of entities) {
      const key = String(entity[field]);
      if (!grouped[key]) {
        grouped[key] = [];
      }
      grouped[key].push(entity);
    }
    
    return grouped as Readonly<Record<string, ReadonlyArray<T>>>;
  }

  /**
   * Find entities created within a date range
   */
  async findByDateRange(startDate: Date, endDate: Date): Promise<ReadonlyArray<T>> {
    const entities = await this.find();
    return entities.filter((entity: any) => {
      const createdAt = new Date(entity.createdAt);
      return createdAt >= startDate && createdAt <= endDate;
    });
  }

  /**
   * Find recently created entities
   */
  async findRecent(limit = 10): Promise<ReadonlyArray<T>> {
    return this.find({
      orderBy: { field: 'createdAt', direction: 'DESC' },
      limit
    });
  }

  /**
   * Find active entities (not soft deleted)
   */
  async findActive(): Promise<ReadonlyArray<T>> {
    const entities = await this.find();
    return entities.filter((entity: any) => !entity.deletedAt);
  }

  /**
   * Find soft deleted entities
   */
  async findDeleted(): Promise<ReadonlyArray<T>> {
    const entities = await this.find();
    return entities.filter((entity: any) => entity.deletedAt);
  }

  /**
   * Restore soft deleted entity
   */
  async restore(id: string): Promise<T | null> {
    return this.update(id, { deletedAt: null } as UpdateEntity<T>);
  }

  /**
   * Permanently delete all soft deleted entities
   */
  async purgeDeleted(): Promise<number> {
    const deleted = await this.findDeleted();
    let count = 0;
    
    for (const entity of deleted) {
      if (await this.delete(entity.id, false)) {
        count++;
      }
    }
    
    return count;
  }

  /**
   * Paginate entities
   */
  async paginate(page = 1, limit = 20, query: RedisQuery<T> = {}): Promise<{
    readonly data: ReadonlyArray<T>;
    readonly pagination: {
      readonly page: number;
      readonly limit: number;
      readonly total: number;
      readonly totalPages: number;
      readonly hasNext: boolean;
      readonly hasPrev: boolean;
    };
  }> {
    const offset = (page - 1) * limit;
    const total = await this.count(query);
    
    const data = await this.find({
      ...query,
      limit,
      offset
    });

    const totalPages = Math.ceil(total / limit);

    return {
      data,
      pagination: {
        page,
        limit,
        total,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1
      }
    };
  }

  /**
   * Start a transaction (for complex operations)
   */
  async transaction(): Promise<RedisTransaction> {
    return this.redisService.transaction();
  }

  /**
   * Search entities by text (basic implementation)
   * Override in concrete repositories for entity-specific search
   */
  async search(query: string, fields: ReadonlyArray<keyof T> = []): Promise<ReadonlyArray<T>> {
    const entities = await this.find();
    const searchLower = query.toLowerCase();
    
    return entities.filter((entity: any) => {
      // If specific fields are provided, search only in those fields
      if (fields.length > 0) {
        return fields.some(field => {
          const value = entity[field];
          return typeof value === 'string' && value.toLowerCase().includes(searchLower);
        });
      }
      
      // Otherwise, search in all string fields
      return Object.values(entity).some(value => 
        typeof value === 'string' && value.toLowerCase().includes(searchLower)
      );
    });
  }

  /**
   * Get entity statistics
   */
  async getStats(): Promise<{
    total: number;
    active: number;
    deleted: number;
    createdToday: number;
    createdThisWeek: number;
    createdThisMonth: number;
  }> {
    const entities = await this.find();
    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    const weekAgo = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);
    const monthAgo = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000);

    return {
      total: entities.length,
      active: entities.filter((e: any) => !e.deletedAt).length,
      deleted: entities.filter((e: any) => e.deletedAt).length,
      createdToday: entities.filter((e: any) => new Date(e.createdAt) >= today).length,
      createdThisWeek: entities.filter((e: any) => new Date(e.createdAt) >= weekAgo).length,
      createdThisMonth: entities.filter((e: any) => new Date(e.createdAt) >= monthAgo).length,
    };
  }
}
